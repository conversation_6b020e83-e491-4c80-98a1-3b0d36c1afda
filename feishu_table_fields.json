[{"field_id": "fld4R2LnO8", "field_name": "序号", "is_primary": true, "property": {"formatter": "0"}, "type": 2, "ui_type": "Number"}, {"field_id": "fld5MR5BUP", "field_name": "报告编号", "is_primary": false, "property": null, "type": 1, "ui_type": "Text"}, {"field_id": "fld8AdSk4j", "field_name": "受理时间", "is_primary": false, "property": null, "type": 1, "ui_type": "Text"}, {"field_id": "fldVdshpyZ", "field_name": "委托单位", "is_primary": false, "property": {"options": [{"color": 0, "id": "optsrPSBAT", "name": "上海闪快信息科技有限公司"}, {"color": 1, "id": "optESgeher", "name": "安徽汉升工业部件股份有限公司"}]}, "type": 3, "ui_type": "SingleSelect"}, {"field_id": "fld2Ay2ECR", "field_name": "样品名称", "is_primary": false, "property": null, "type": 1, "ui_type": "Text"}, {"field_id": "fld2U524ID", "field_name": "款号", "is_primary": false, "property": null, "type": 1, "ui_type": "Text"}, {"field_id": "fld2mmTff4", "field_name": "颜色", "is_primary": false, "property": null, "type": 1, "ui_type": "Text"}, {"field_id": "fld6AYbY9E", "field_name": "商标", "is_primary": false, "property": {"options": [{"color": 0, "id": "optIHQm6t3", "name": "亚朵星球"}, {"color": 1, "id": "optdg7uJOs", "name": "闪快"}]}, "type": 3, "ui_type": "SingleSelect"}, {"field_id": "fld65tJUvO", "field_name": "面料编号", "is_primary": false, "property": {"options": [{"color": 0, "id": "optbukdLHL", "name": "LD25025"}, {"color": 1, "id": "optTglCwXu", "name": "WF250045"}, {"color": 2, "id": "optlFRMhRY", "name": "WF250044"}, {"color": 3, "id": "optMjxBXUc", "name": "KPSD00211"}]}, "type": 3, "ui_type": "SingleSelect"}, {"field_id": "fld3V6ngdl", "field_name": "生产单位", "is_primary": false, "property": {"options": [{"color": 0, "id": "opta9P1ZDW", "name": "---"}, {"color": 1, "id": "optn6Decpz", "name": "上海闪快信息科技有限公司"}, {"color": 2, "id": "optOyn5Bm3", "name": "中拓生物科技（南通）有限公司"}, {"color": 3, "id": "optw8Ds9vV", "name": "东莞超盈纺织有限公司"}, {"color": 4, "id": "optjqy99pY", "name": "互太（番禺）纺织印染有限公司"}, {"color": 5, "id": "optAMngwYu", "name": "青岛迦喜家纺有限公司"}]}, "type": 3, "ui_type": "SingleSelect"}, {"field_id": "fld3OVA1u3", "field_name": "缴费单位", "is_primary": false, "property": {"options": [{"color": 0, "id": "optnUia7MA", "name": "上海闪快信息科技有限公司"}, {"color": 1, "id": "optJ0MEWFm", "name": "苏州飞羽盛纺织科技有限公司"}, {"color": 2, "id": "optPew0hT2", "name": "东莞超盈纺织有限公司"}, {"color": 3, "id": "optoQXhMww", "name": "互太（番禺）纺织印染有限公司"}, {"color": 4, "id": "optmvKxEUT", "name": "嘉兴市中深爱的家居科技有限公司"}, {"color": 5, "id": "optUXjBbHz", "name": "青岛迦喜家纺有限公司"}, {"color": 6, "id": "optsZDkFhf", "name": "喜临门家具股份有限公司"}]}, "type": 3, "ui_type": "SingleSelect"}, {"field_id": "fld5vuWKBE", "field_name": "检验项目", "is_primary": false, "property": null, "type": 1, "ui_type": "Text"}, {"field_id": "fld7mRXGfP", "field_name": "检测进度", "is_primary": false, "property": {"options": [{"color": 0, "id": "optj5vpN0a", "name": "已受理"}, {"color": 1, "id": "opt3PeYlXw", "name": "正在检测"}, {"color": 2, "id": "opta3xaDPM", "name": "报告审核"}, {"color": 3, "id": "optMYsfntT", "name": "已出证"}]}, "type": 3, "ui_type": "SingleSelect"}, {"field_id": "fldsNGPcxv", "field_name": "报告结果", "is_primary": false, "property": {"options": [{"color": 0, "id": "optG5B7nPT", "name": "不合格"}, {"color": 1, "id": "optvL0GNFP", "name": "合格"}, {"color": 2, "id": "optA7gXIQ0", "name": "---"}]}, "type": 3, "ui_type": "SingleSelect"}, {"field_id": "fld4MmrnoQ", "field_name": "不合格项目", "is_primary": false, "property": null, "type": 1, "ui_type": "Text"}, {"field_id": "fld6H46ukt", "field_name": "预计发证时间", "is_primary": false, "property": null, "type": 1, "ui_type": "Text"}, {"field_id": "fld3b3AtdY", "field_name": "出证时间", "is_primary": false, "property": null, "type": 1, "ui_type": "Text"}, {"field_id": "fld4e9rkTv", "field_name": "金额", "is_primary": false, "property": {"formatter": "0"}, "type": 2, "ui_type": "Number"}, {"field_id": "fld5Zm45sq", "field_name": "费用交付状态", "is_primary": false, "property": {"options": [{"color": 0, "id": "optS9M8fhs", "name": "未付"}, {"color": 1, "id": "optzN39cVx", "name": "费用收讫"}]}, "type": 3, "ui_type": "SingleSelect"}, {"field_id": "fld2XVDcUz", "field_name": "受理单", "is_primary": false, "property": null, "type": 1, "ui_type": "Text"}, {"field_id": "fld2bYn6Dz", "field_name": "缴费通知单", "is_primary": false, "property": {"options": [{"color": 0, "id": "optPOU7LEr", "name": "不可用"}, {"color": 1, "id": "optx0kN5ve", "name": "可下载"}]}, "type": 3, "ui_type": "SingleSelect"}, {"field_id": "fldcgvylY8", "field_name": "报告书", "is_primary": false, "property": {"options": [{"color": 0, "id": "opt0bOe9N8", "name": "不可用"}, {"color": 1, "id": "optW077VYa", "name": "可下载"}]}, "type": 3, "ui_type": "SingleSelect"}, {"field_id": "fld59HVYne", "field_name": "报告寄出信息", "is_primary": false, "property": null, "type": 1, "ui_type": "Text"}, {"field_id": "fldIpGTPGe", "field_name": "受理单下载链接", "is_primary": false, "property": null, "type": 15, "ui_type": "Url"}, {"field_id": "fld4DaxreU", "field_name": "缴费通知单下载链接", "is_primary": false, "property": null, "type": 15, "ui_type": "Url"}, {"field_id": "flddX8yJJb", "field_name": "报告书下载链接", "is_primary": false, "property": null, "type": 1, "ui_type": "Text"}, {"field_id": "fld1NEifsg", "field_name": "受理单_链接", "is_primary": false, "property": null, "type": 15, "ui_type": "Url"}, {"field_id": "fld5NgcLlJ", "field_name": "缴费通知单_链接", "is_primary": false, "property": null, "type": 15, "ui_type": "Url"}, {"field_id": "fld26XXHfb", "field_name": "报告书_链接", "is_primary": false, "property": null, "type": 15, "ui_type": "Url"}, {"field_id": "fld12ju47M", "field_name": "选中", "is_primary": false, "property": null, "type": 1, "ui_type": "Text"}, {"field_id": "fld2JVuoLf", "field_name": "备注", "is_primary": false, "property": null, "type": 1, "ui_type": "Text"}]