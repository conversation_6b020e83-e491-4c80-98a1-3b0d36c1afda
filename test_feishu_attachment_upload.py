import os
import requests
import feishu_config as config
import feishu_uploader_utils as feishu_utils

def test_upload_single_attachment():
    """
    Tests uploading a single attachment to Feishu.
    """
    print("Starting Feishu attachment upload test...")

    # 1. Get Access Token
    print("Fetching access token...")
    access_token = feishu_utils.get_tenant_access_token()
    if not access_token:
        print("Failed to get access token. Aborting test.")
        return

    print(f"Successfully got access token: {access_token[:20]}...") # Print only a part for brevity

    # 2. Define file to upload and parameters
    file_to_upload = "sample_attachment.txt" # Ensure this file exists in the root
    if not os.path.exists(file_to_upload):
        print(f"Test file '{file_to_upload}' not found. Please create it. Aborting test.")
        # Create a dummy file if it doesn't exist for the test to run
        with open(file_to_upload, "w") as f:
            f.write("This is a test file for Feishu attachment upload.")
        print(f"Created dummy file: {file_to_upload}")


    app_token = config.APP_TOKEN
    file_name = os.path.basename(file_to_upload)
    file_size = os.path.getsize(file_to_upload)

    print(f"Attempting to upload: {file_name}, Size: {file_size} bytes")
    print(f"Parent Type: bitable_file, Parent Node (App Token): {app_token}")

    # 3. Construct the request
    url = "https://open.feishu.cn/open-apis/drive/v1/medias/upload_all"
    headers = {
        "Authorization": f"Bearer {access_token}"
        # 'Content-Type' will be set by requests when using files parameter with multipart/form-data
    }
    
    form_data = {
        'file_name': file_name,
        'parent_type': 'bitable_file', # Correct type for Bitable attachments
        'parent_node': app_token,
        'size': str(file_size), # Size must be a string
    }
    
    files = {
        'file': (file_name, open(file_to_upload, 'rb'))
    }

    # 4. Make the API call
    print("Sending request to Feishu API...")
    try:
        response = requests.post(url, headers=headers, data=form_data, files=files, timeout=60)
        
        print(f"Response Status Code: {response.status_code}")
        print(f"Response Body: {response.text}")

        if response.status_code == 200:
            response_json = response.json()
            if response_json.get("code") == 0:
                file_token = response_json.get("data", {}).get("file_token")
                if file_token:
                    print(f"SUCCESS! File uploaded. File Token: {file_token}")
                else:
                    print("ERROR: API success code 0, but no file_token in response data.")
            else:
                print(f"ERROR: API call failed with code {response_json.get('code')} and message: {response_json.get('msg')}")
        else:
            print("ERROR: API call failed with non-200 status code.")

    except requests.exceptions.RequestException as e:
        print(f"An exception occurred during the request: {e}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
    finally:
        # Close the file if it was opened
        if 'file' in files and files['file'][1]:
            files['file'][1].close()
        print("Test finished.")

if __name__ == "__main__":
    test_upload_single_attachment() 