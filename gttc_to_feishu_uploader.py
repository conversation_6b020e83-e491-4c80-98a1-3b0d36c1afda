import json
import os
import time
import requests
from urllib.parse import urlparse, unquote, urljoin
import re
from datetime import datetime, timedelta
import concurrent.futures
import threading
import random

# 导入自定义模块
import feishu_config as config
import feishu_uploader_utils as feishu_utils
from gttc_scraper import GTTCReportScraper

# 临时附件下载目录
TEMP_DOWNLOAD_DIR = "temp_attachments"
if not os.path.exists(TEMP_DOWNLOAD_DIR):
    os.makedirs(TEMP_DOWNLOAD_DIR)

# 飞书API并发控制
FEISHU_UPLOAD_SEMAPHORE = threading.Semaphore(5)  # 限制同时上传的文件数量为2个
FEISHU_RATE_LIMIT_DELAY = 0.5  # 每次上传后的基础延迟（秒）

def sanitize_filename(filename):
    """清理文件名，移除或替换无效字符"""
    filename = re.sub(r'[\\/:*?"<>|]', '_', filename)
    filename = filename.replace('\n', ' ').replace('\r', ' ')
    return filename.strip()

def upload_attachment(file_path, app_token, access_token, report_id="", max_retries=3):
    """上传素材接口 - 包含并发控制和重试逻辑"""
    url = "https://open.feishu.cn/open-apis/drive/v1/medias/upload_all"
    headers = {
        "Authorization": f"Bearer {access_token}"
    }

    file_name = os.path.basename(file_path)
    file_size = os.path.getsize(file_path)

    form_data = {
        'file_name': file_name,
        'parent_type': 'bitable_file', # 多维表格附件类型
        'parent_node': app_token,      # 多维表格的 app_token
        'size': str(file_size),        # 文件大小，字符串形式
    }

    # 使用信号量控制并发
    with FEISHU_UPLOAD_SEMAPHORE:
        for attempt in range(max_retries):
            try:
                with open(file_path, 'rb') as f:
                    files = {'file': (file_name, f)} # 文件内容
                    if attempt > 0:  # 只在重试时显示尝试次数
                        print(f"[报告 {report_id}] 重试上传 (尝试 {attempt + 1}/{max_retries}): {file_name}")
                    response = requests.post(url, headers=headers, data=form_data, files=files, timeout=120)

                # 检查是否遇到速率限制
                if response.status_code == 429:
                    retry_after = int(response.headers.get('Retry-After', 60))
                    print(f"[报告 {report_id}] 遇到速率限制，等待 {retry_after} 秒后重试...")
                    time.sleep(retry_after)
                    continue

                if response.status_code == 200:
                    response_json = response.json()
                    if response_json.get("code") == 0:
                        file_token = response_json.get("data", {}).get("file_token")
                        if file_token:
                            # 成功后添加延迟以避免过快请求
                            delay = FEISHU_RATE_LIMIT_DELAY + random.uniform(0, 0.3)
                            time.sleep(delay)
                            return file_token
                        else:
                            print(f"[报告 {report_id}] 上传成功但未在响应中找到 file_token: {response.text}")
                            return None
                    elif response_json.get("code") == 99991400:  # 飞书常见的速率限制错误码
                        print(f"[报告 {report_id}] 飞书API速率限制，等待后重试... (尝试 {attempt + 1}/{max_retries})")
                        delay = (2 ** attempt) + random.uniform(0, 1)  # 指数退避
                        time.sleep(delay)
                        continue
                    else:
                        print(f"[报告 {report_id}] 飞书 API 错误: code={response_json.get('code')}, msg={response_json.get('msg')}")
                        if attempt == max_retries - 1:  # 最后一次尝试
                            return None
                        delay = (2 ** attempt) + random.uniform(0, 1)
                        time.sleep(delay)
                        continue
                else:
                    print(f"[报告 {report_id}] 上传失败: HTTP {response.status_code} - {response.text}")
                    if attempt == max_retries - 1:  # 最后一次尝试
                        return None
                    delay = (2 ** attempt) + random.uniform(0, 1)
                    time.sleep(delay)
                    continue

            except requests.exceptions.Timeout:
                print(f"[报告 {report_id}] 上传文件 {file_name} 超时 (尝试 {attempt + 1}/{max_retries})")
                if attempt == max_retries - 1:
                    return None
                time.sleep(2 ** attempt)
                continue
            except requests.exceptions.RequestException as e:
                print(f"[报告 {report_id}] 上传文件时发生 requests 错误: {e} (尝试 {attempt + 1}/{max_retries})")
                if attempt == max_retries - 1:
                    return None
                time.sleep(2 ** attempt)
                continue
            except Exception as e:
                print(f"[报告 {report_id}] 上传文件时发生未知错误: {e} (尝试 {attempt + 1}/{max_retries})")
                if attempt == max_retries - 1:
                    return None
                time.sleep(2 ** attempt)
                continue

        return None

def download_file(url, download_path, session, report_id=""):
    """下载文件到指定路径，使用报告ID确保文件名唯一"""
    if not url or not url.startswith(('http:', 'https:')):
        print(f"[报告 {report_id}] 无效的下载链接: {url}")
        return None
    try:
        # GTTC网站的链接可能是相对的，需要补全
        if not urlparse(url).netloc:
            url = urljoin(GTTCReportScraper().base_url, url)

        print(f"[报告 {report_id}] 尝试下载附件: {url}")
        response = session.get(url, stream=True, timeout=60, verify=False)
        response.raise_for_status()

        # 尝试从 Content-Disposition 获取文件名
        content_disposition = response.headers.get('content-disposition')
        original_filename = None
        if content_disposition:
            filenames = re.findall('filename=(.?)([^;\\n]*)"*', content_disposition)
            if filenames:
                original_filename = unquote(filenames[0][1])

        if not original_filename:
            # 如果无法从header获取，则从URL路径中提取
            parsed_url = urlparse(url)
            original_filename = os.path.basename(parsed_url.path)
            if not original_filename:
                original_filename = f"downloaded_file_{int(time.time())}"

        sanitized_original_filename = sanitize_filename(original_filename)

        # 使用报告ID和时间戳确保文件名唯一
        name_part, ext_part = os.path.splitext(sanitized_original_filename)
        timestamp = int(time.time() * 1000)  # 毫秒级时间戳
        unique_filename = f"{report_id}_{timestamp}_{name_part[:50]}{ext_part}"

        file_full_path = os.path.join(download_path, unique_filename)

        with open(file_full_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        print(f"[报告 {report_id}] 文件已下载: {file_full_path}")
        return file_full_path
    except requests.exceptions.RequestException as e:
        print(f"[报告 {report_id}] 下载文件失败 {url}: {e}")
        return None
    except Exception as e:
        print(f"[报告 {report_id}] 下载或保存文件时发生未知错误 {url}: {e}")
        return None

def process_single_report(report_data, scraper_session, access_token, item_idx, total_items):
    """处理单条报告数据，包括下载和上传附件"""
    report_id = report_data.get('报告编号', 'N/A')
    print(f"\n[报告 {report_id}] 开始处理第 {item_idx}/{total_items} 条报告...")
    feishu_record_fields = {}

    # 1. 字段映射 (不包括附件链接)
    # 定义字段类型（基于飞书表格字段类型）
    number_fields = {'序号', '金额'}  # type: 2, Number类型
    single_select_fields = {
        '委托单位', '商标', '面料编号', '生产单位', '缴费单位',
        '检测进度', '报告结果', '费用交付状态', '缴费通知单', '报告书'
    }  # type: 3, SingleSelect类型

    for scraper_key, feishu_key in config.FIELD_MAPPING.items():
        if feishu_key and scraper_key in report_data and not scraper_key.endswith('下载链接'):
            value = report_data[scraper_key]

            # 处理数字字段（序号、金额等）
            if feishu_key in number_fields and value is not None:
                if isinstance(value, str) and value.strip() == "": # 如果是空字符串
                    feishu_record_fields[feishu_key] = None
                elif isinstance(value, (int, float)): # 如果已经是数字
                     feishu_record_fields[feishu_key] = value
                else: # 尝试转换字符串为数字
                    try:
                        # 移除可能的千位分隔符和其他非数字字符
                        clean_value = str(value).replace(',', '').strip()
                        num_value = float(clean_value)
                        if num_value.is_integer():
                            feishu_record_fields[feishu_key] = int(num_value)
                        else:
                            feishu_record_fields[feishu_key] = num_value
                    except ValueError:
                        print(f"[报告 {report_id}] 警告: 字段 '{scraper_key}' (映射到 '{feishu_key}') 的值 '{value}' 无法转换为数字，将置为空。")
                        feishu_record_fields[feishu_key] = None
            # 处理单选字段 - 直接传递字符串值，飞书会自动匹配选项
            elif feishu_key in single_select_fields:
                if value is not None and str(value).strip():
                    feishu_record_fields[feishu_key] = str(value).strip()
                else:
                    feishu_record_fields[feishu_key] = None
            elif isinstance(value, (int, float)): # 对于其他已经是数字的字段
                feishu_record_fields[feishu_key] = value
            else: # 对于其他字符串字段
                feishu_record_fields[feishu_key] = str(value).strip() if value is not None else ""

    # 2. 处理三个附件字段
    attachment_mappings = {
        '受理单下载链接': '受理单下载链接',
        '缴费通知单下载链接': '缴费通知单下载链接',
        '报告书下载链接': '报告书下载链接'  # 修改：修正字段名以匹配CSV/飞书表格
    }

    for scraper_key, feishu_field_name in attachment_mappings.items():
        # 初始化附件字段为空列表
        feishu_record_fields[feishu_field_name] = []

        download_url = report_data.get(scraper_key)

        if download_url and str(download_url).strip() and str(download_url).lower() != "不可用":
            # 特殊处理报告书下载链接：如果只是报告编号，说明报告书还未生成或不可用
            if scraper_key == '报告书下载链接' and not str(download_url).startswith(('http:', 'https:')):
                print(f"[报告 {report_id}] 报告书下载链接只有编号 '{download_url}'，可能报告书还未生成，跳过下载。")
                # 确保该字段存在于feishu_record_fields中，并设置为空列表
                if feishu_field_name not in feishu_record_fields:
                     feishu_record_fields[feishu_field_name] = []
                continue
            else:
                # 确保URL是绝对的
                if not urlparse(str(download_url)).scheme:
                    download_url = urljoin(GTTCReportScraper().base_url, str(download_url))

                downloaded_file_path = download_file(str(download_url), TEMP_DOWNLOAD_DIR, scraper_session, report_id)

            if downloaded_file_path:
                print(f"[报告 {report_id}] 准备上传 {os.path.basename(downloaded_file_path)} 到飞书...")

                file_token = upload_attachment(downloaded_file_path, config.APP_TOKEN, access_token, report_id)

                if file_token:
                    feishu_record_fields[feishu_field_name].append({"file_token": file_token})
                    print(f"[报告 {report_id}] 成功上传附件并获取 file_token: {file_token}")
                else:
                    print(f"[报告 {report_id}] 上传附件 {os.path.basename(downloaded_file_path)} 失败。")

                try:
                    os.remove(downloaded_file_path)
                    print(f"[报告 {report_id}] 已删除临时文件: {os.path.basename(downloaded_file_path)}")
                except OSError as e:
                    print(f"[报告 {report_id}] 删除临时文件 {os.path.basename(downloaded_file_path)} 失败: {e}")
            else:
                print(f"[报告 {report_id}] 下载附件 {scraper_key} ({download_url}) 失败，无法上传。")
        else:
            # 确保即使链接无效，该字段也存在于feishu_record_fields中，并设置为空列表
            if feishu_field_name not in feishu_record_fields:
                feishu_record_fields[feishu_field_name] = []
            print(f"[报告 {report_id}] 附件 {scraper_key} 链接无效或不可用: '{download_url}'")

    return {"fields": feishu_record_fields}

def main():
    print("开始执行GTTC报告数据到飞书多维表的上传流程...")

    # 获取飞书access_token
    print("获取飞书access_token...")
    access_token = feishu_utils.get_tenant_access_token()
    if not access_token:
        print("无法获取飞书access_token，程序终止。")
        return

    # 1. 初始化GTTC抓取器并抓取指定日期的数据
    print("\n步骤 1: 从GTTC抓取报告数据...")
    scraper = GTTCReportScraper()

    # 配置日期筛选为最近3天（用于测试）
    today = datetime.now()
    start_date = today - timedelta(days=3)
    end_date = today

    start_date_str = start_date.strftime("%Y-%m-%d")
    end_date_str = end_date.strftime("%Y-%m-%d")

    print(f"正在获取日期范围 {start_date_str} 到 {end_date_str} 的数据（最近3天）...")
    date_params = {
        "Report_Date1": start_date_str,
        "Report_Date2": end_date_str,
    }

    response = scraper.make_request(params=date_params)
    if not response:
        print("从GTTC获取数据失败，程序终止。")
        return

    gttc_data = scraper.parse_html_table(response.text)
    if not gttc_data:
        print("未从GTTC解析到任何数据，程序终止。")
        return

    print(f"成功从GTTC抓取并解析到 {len(gttc_data)} 条报告数据。")

    # 限制处理的记录数量（用于测试）
    MAX_RECORDS_TO_PROCESS = 10
    if len(gttc_data) > MAX_RECORDS_TO_PROCESS:
        print(f"为了测试安全，将只处理前 {MAX_RECORDS_TO_PROCESS} 条记录（共 {len(gttc_data)} 条）")
        gttc_data = gttc_data[:MAX_RECORDS_TO_PROCESS]
    else:
        print(f"将处理全部 {len(gttc_data)} 条记录")

    # 打印第一条记录作为预览
    if gttc_data:
        print("\n第一条抓取到的记录预览 (部分):")
        for key, value in list(gttc_data[0].items())[:7]:
            print(f"  {key}: {value}")
        if len(gttc_data[0]) > 7:
            print(f"  ... (还有 {len(gttc_data[0]) - 7} 个字段)")

    # 2. 清空飞书多维表中的所有旧数据
    print(f"\n步骤 2: 清空飞书多维表 (App: {config.APP_TOKEN}, Table: {config.TABLE_ID}) 中的所有记录...")
    if not feishu_utils.clear_all_records(config.APP_TOKEN, config.TABLE_ID):
        print("清空飞书表格失败，程序终止以防止数据不一致。")
        print("警告: 清空飞书表格失败，将继续尝试上传新数据。这可能导致数据重复或混合。")
    else:
        print("飞书多维表已成功清空。")

    # 3. 并发处理抓取到的数据并上传附件
    print("\n步骤 3: 处理抓取到的数据并上传附件 (并发处理)...")
    feishu_records_to_upload = []
    MAX_WORKERS = 3  # 并发数，配合信号量控制以避免API限制

    with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        # 构建future到报告原始数据的映射，方便后续获取报告编号进行日志记录
        future_to_report_map = {}
        for i, report_item in enumerate(gttc_data):
            # 提交任务到线程池，传递新的日志参数 item_idx 和 total_items
            future = executor.submit(process_single_report, report_item, scraper.session, access_token, i + 1, len(gttc_data))
            future_to_report_map[future] = report_item

        for future in concurrent.futures.as_completed(future_to_report_map):
            original_report_item = future_to_report_map[future]
            report_id_for_log = original_report_item.get('报告编号', 'N/A')
            try:
                processed_record = future.result() # 获取任务执行结果
                if processed_record and processed_record.get("fields"):
                    feishu_records_to_upload.append(processed_record)
                else:
                    print(f"警告: 报告 (编号: {report_id_for_log}) 处理后返回空或无效结果，将跳过此条记录。详情请查看该报告处理日志。")
            except Exception as exc:
                print(f"报告 (编号: {report_id_for_log}) 在并发处理过程中发生错误: {exc}")
                # 这里可以添加更详细的错误日志或将失败的报告收集起来

    # time.sleep(0.2) # 原有的延时，在并发模型下通常不需要，移除

    if not feishu_records_to_upload:
        print("没有成功处理任何记录以供上传到飞书。程序终止。")
        return

    print(f"\n共准备了 {len(feishu_records_to_upload)} 条记录待上传到飞书。")
    if feishu_records_to_upload:
        print("预览第一条待上传到飞书的记录 (处理后): ")
        print(json.dumps(feishu_records_to_upload[0], ensure_ascii=False, indent=2))

    # 4. 批量上传数据到飞书
    print(f"\n步骤 4: 批量添加处理后的记录到飞书多维表...")
    success_upload, added_count, failed_upload_info = feishu_utils.batch_add_records(
        config.APP_TOKEN,
        config.TABLE_ID,
        feishu_records_to_upload
    )

    if success_upload:
        print(f"\n成功上传 {added_count} 条记录到飞书多维表！")
    else:
        print(f"\n上传记录到飞书多维表过程中发生错误。")
        print(f"成功添加 {added_count} 条记录。")
        if failed_upload_info:
            print(f"{len(failed_upload_info)} 条记录可能上传失败。")

    # 清理临时下载目录中的剩余文件
    try:
        for item in os.listdir(TEMP_DOWNLOAD_DIR):
            item_path = os.path.join(TEMP_DOWNLOAD_DIR, item)
            if os.path.isfile(item_path):
                os.remove(item_path)
        if os.path.exists(TEMP_DOWNLOAD_DIR) and not os.listdir(TEMP_DOWNLOAD_DIR):
            os.rmdir(TEMP_DOWNLOAD_DIR)
            print(f"已成功清理并删除临时附件目录: {TEMP_DOWNLOAD_DIR}")
        elif os.path.exists(TEMP_DOWNLOAD_DIR):
            print(f"临时附件目录 {TEMP_DOWNLOAD_DIR} 已清理，但目录本身未删除（可能因为权限或仍有内容）。")
        else:
            print(f"临时附件目录 {TEMP_DOWNLOAD_DIR} 未找到，无需清理。")
    except OSError as e:
        print(f"清理临时附件目录 {TEMP_DOWNLOAD_DIR} 失败: {e}。可能需要手动清理。")

    print("\nGTTC报告数据到飞书多维表的上传流程结束。")

if __name__ == "__main__":
    main()