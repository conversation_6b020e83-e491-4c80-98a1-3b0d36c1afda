#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GTTC质检报告数据抓取工具
模拟请求并解析数据为JSON和CSV格式
"""

import requests
from bs4 import BeautifulSoup
import json
import csv
import re
from datetime import datetime
from urllib.parse import urljoin
import urllib3

# 禁用SSL证书警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class GTTCReportScraper:
    def __init__(self):
        self.base_url = "https://www.gttc.net.cn"
        self.session = requests.Session()
        
        # 设置请求头，模拟浏览器
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'dnt': '1',
            'upgrade-insecure-requests': '1',
            'sec-fetch-site': 'cross-site',
            'sec-fetch-mode': 'navigate',
            'sec-fetch-user': '?1',
            'sec-fetch-dest': 'document',
            'referer': 'https://www.gttctech.com/',
            'accept-language': 'en,zh-CN;q=0.9,zh;q=0.8',
            'priority': 'u=0, i'
        }
        
        self.session.headers.update(self.headers)

    def make_request(self, params=None):
        """
        发送请求到GTTC网站
        
        Args:
            params (dict): 查询参数，如果为None则使用默认参数
        
        Returns:
            requests.Response: 响应对象
        """
        url = f"{self.base_url}/Home/ShowSearchReportResult"
        
        # 默认参数（来自原始curl请求）
        default_params = {
            'ReortNo': '',
            'Color': '',
            'AccceptTimeFrom': '2025-01-30',
            'AccceptTimeTo': '2025-05-30',
            'CertTimeFrom': '',
            'CertTimeTo': '',
            'DelegateOrgName': '',
            'BatchNo': '',
            'ProductOrgName': '',
            'Matior': '',
            'ChargeOrgName': '',
            'SampleName': '',
            'CheckItemName': '',
            'Brand': '',
            'IsPassReport': '1',
            'GF': 'gtt8047',
            'GJ': '160e5ac50c68cb92350823216b6982b5'
        }
        
        if params:
            default_params.update(params)
        
        try:
            response = self.session.get(url, params=default_params, verify=False, timeout=30)
            response.raise_for_status()
            return response
        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")
            return None

    def parse_html_table(self, html_content):
        """
        解析HTML表格数据
        
        Args:
            html_content (str): HTML内容
        
        Returns:
            list: 解析后的数据列表
        """
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 查找主要的数据表格
        table = soup.find('table', {'id': 'reportSearchTable'})
        if not table:
            print("未找到数据表格")
            return []
        
        # 获取表头
        thead = table.find('thead')
        if not thead:
            print("未找到表头")
            return []
        
        headers = []
        header_row = thead.find('tr')
        if header_row:
            for th in header_row.find_all('th'):
                header_text = th.get_text(strip=True)
                headers.append(header_text)
        
        # 获取数据行
        tbody = table.find('tbody')
        if not tbody:
            print("未找到表格数据")
            return []
        
        data_list = []
        rows = tbody.find_all('tr')
        
        for row in rows:
            cells = row.find_all('td')
            if len(cells) < len(headers):
                continue
            
            row_data = {}
            
            for i, cell in enumerate(cells):
                if i >= len(headers):
                    break
                
                header = headers[i]
                
                # 特殊处理不同类型的单元格
                if header == "选中":
                    # 提取隐藏的textarea中的信息
                    textareas = cell.find_all('textarea', style=re.compile(r'display:\s*none'))
                    for textarea in textareas:
                        textarea_id = textarea.get('id', '')
                        if 'areAccept' in textarea_id:
                            row_data['受理单下载链接'] = textarea.get_text(strip=True)
                        elif 'areCharge' in textarea_id:
                            row_data['缴费通知单下载链接'] = textarea.get_text(strip=True)
                        elif 'areReport' in textarea_id:
                            row_data['报告书下载链接'] = textarea.get_text(strip=True)
                        elif 'areReportNo' in textarea_id:
                            row_data['报告编号_hidden'] = textarea.get_text(strip=True)
                        elif 'areAmount' in textarea_id:
                            row_data['金额'] = textarea.get_text(strip=True)
                    
                    row_data[header] = "否"  # 默认未选中
                
                elif header in ["受理单", "缴费通知单", "报告书"]:
                    # 查找下载链接
                    link = cell.find('a')
                    if link and link.get('href'):
                        row_data[f"{header}_链接"] = link.get('href')
                        row_data[header] = "可下载" if link.get_text(strip=True) else "不可用"
                    else:
                        row_data[header] = "不可用"
                        row_data[f"{header}_链接"] = ""
                
                else:
                    # 普通文本内容
                    cell_text = cell.get_text(strip=True)
                    # 清理文本中的多余空白
                    cell_text = re.sub(r'\s+', ' ', cell_text)
                    row_data[header] = cell_text
            
            if row_data:
                data_list.append(row_data)
        
        return data_list

    def save_to_json(self, data, filename="gttc_reports.json"):
        """
        保存数据为JSON格式
        
        Args:
            data (list): 要保存的数据
            filename (str): 文件名
        """
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump({
                    'timestamp': datetime.now().isoformat(),
                    'total_records': len(data),
                    'data': data
                }, f, ensure_ascii=False, indent=2)
            print(f"JSON数据已保存到: {filename}")
        except Exception as e:
            print(f"保存JSON文件失败: {e}")

    def save_to_csv(self, data, filename="gttc_reports.csv"):
        """
        保存数据为CSV格式
        
        Args:
            data (list): 要保存的数据
            filename (str): 文件名
        """
        if not data:
            print("没有数据可保存")
            return
        
        try:
            # 定义业务逻辑优先的字段顺序
            priority_fields = [
                # 基本信息
                '序号', '报告编号', '受理时间', '委托单位', '样品名称',
                
                # 产品信息
                '款号', '颜色', '商标', '面料编号',
                
                # 单位信息
                '生产单位', '缴费单位',
                
                # 检测信息
                '检验项目', '检测进度', '报告结果', '不合格项目',
                
                # 时间信息
                '预计发证时间', '出证时间',
                
                # 费用信息
                '金额', '费用交付状态',
                
                # 文件状态
                '受理单', '缴费通知单', '报告书', '报告寄出信息',
                
                # 下载链接（业务重要）
                '受理单下载链接', '缴费通知单下载链接', '报告书下载链接',
                
                # 页面链接（技术辅助）
                '受理单_链接', '缴费通知单_链接', '报告书_链接',
                
                # 其他信息
                '选中', '备注'
            ]
            
            # 获取所有实际存在的字段
            all_fields = set()
            for record in data:
                all_fields.update(record.keys())
            
            # 按优先级排序字段
            ordered_fields = []
            
            # 首先添加优先级字段（如果存在的话）
            for field in priority_fields:
                if field in all_fields:
                    ordered_fields.append(field)
                    all_fields.remove(field)
            
            # 然后添加剩余字段（按字母顺序）
            remaining_fields = sorted(list(all_fields))
            ordered_fields.extend(remaining_fields)
            
            # 写入CSV文件
            with open(filename, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.DictWriter(f, fieldnames=ordered_fields)
                writer.writeheader()
                writer.writerows(data)
            
            print(f"CSV数据已保存到: {filename}")
            print(f"字段顺序: {', '.join(ordered_fields[:10])}...")  # 显示前10个字段
        except Exception as e:
            print(f"保存CSV文件失败: {e}")

    def scrape_and_save(self, params=None, json_filename="gttc_reports.json", csv_filename="gttc_reports.csv"):
        """
        执行完整的抓取和保存流程
        
        Args:
            params (dict): 查询参数
            json_filename (str): JSON文件名
            csv_filename (str): CSV文件名
        """
        print("正在发送请求...")
        response = self.make_request(params)
        
        if not response:
            print("请求失败，程序终止")
            return
        
        print(f"请求成功，状态码: {response.status_code}")
        print(f"响应内容长度: {len(response.text)} 字符")
        
        print("正在解析HTML内容...")
        data = self.parse_html_table(response.text)
        
        if not data:
            print("未解析到任何数据")
            return
        
        print(f"成功解析 {len(data)} 条记录")
        
        # 打印第一条记录作为预览
        if data:
            print("\n第一条记录预览:")
            for key, value in list(data[0].items())[:5]:  # 只显示前5个字段
                print(f"  {key}: {value}")
            if len(data[0]) > 5:
                print(f"  ... (还有 {len(data[0]) - 5} 个字段)")
        
        print("\n正在保存数据...")
        self.save_to_json(data, json_filename)
        self.save_to_csv(data, csv_filename)
        
        print("数据处理完成！")


def main():
    """主函数"""
    print("GTTC质检报告数据抓取工具")
    print("=" * 40)
    
    scraper = GTTCReportScraper()
    
    # 可以修改查询参数
    custom_params = {
        'AccceptTimeFrom': '2025-01-01',
        'AccceptTimeTo': '2025-05-30',
        'IsPassReport': '1'
    }
    
    scraper.scrape_and_save(
        params=custom_params,
        json_filename="gttc_reports.json",
        csv_filename="gttc_reports.csv"
    )


if __name__ == "__main__":
    main() 