#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试报告书URL构造逻辑
"""

def test_url_construction():
    """测试URL构造逻辑"""
    
    # 模拟测试数据
    test_data = {
        '报告编号': '250151320',
        '报告书': '可下载',
        '检测进度': '已出证',
        '受理时间': '2025-05-30 16:59',
        '报告书下载链接': '250151320'
    }
    
    print("测试URL构造逻辑")
    print("=" * 50)
    
    report_id = test_data['报告编号']
    download_url_str = test_data['报告书下载链接']
    accept_time = test_data['受理时间']
    
    print(f"输入数据:")
    print(f"  报告编号: {report_id}")
    print(f"  受理时间: {accept_time}")
    print(f"  下载链接: {download_url_str}")
    
    # 构造URL逻辑
    report_number = download_url_str
    possible_urls = []
    
    # 从受理时间推断可能的路径
    if accept_time:
        try:
            # 解析受理时间 "2025-05-30 16:59"
            date_part = accept_time.split(' ')[0]  # "2025-05-30"
            year, month, day = date_part.split('-')
            yyyymm = f"{year}{month}"
            yyyymmdd = f"{year}{month}{day}"
            
            # 构造可能的URL
            possible_urls.extend([
                f"https://www.gttc.net.cn/down/NewSystem/{yyyymm}/{yyyymmdd}/{report_number}.pdf",
                f"https://www.gttc.net.cn/down/NewSystem//{yyyymm}/{yyyymmdd}/{report_number}.pdf",
                f"https://www.gttc.net.cn/down/NewSystem/{yyyymm}/{yyyymmdd}/report/{report_number}.pdf",
                f"https://www.gttc.net.cn/down/report/{report_number}.pdf",
                f"https://www.gttc.net.cn/down/{report_number}.pdf"
            ])
        except Exception as e:
            print(f"解析受理时间失败: {e}")
    
    # 添加通用的尝试URL
    possible_urls.extend([
        f"https://www.gttc.net.cn/down/NewSystem/report/{report_number}.pdf",
        f"https://www.gttc.net.cn/down/report/{report_number}.pdf",
        f"https://www.gttc.net.cn/down/{report_number}.pdf"
    ])
    
    print(f"\n构造的可能URL:")
    for i, url in enumerate(possible_urls, 1):
        print(f"  {i}. {url}")
    
    print(f"\n总共构造了 {len(possible_urls)} 个可能的URL")

if __name__ == "__main__":
    test_url_construction()
