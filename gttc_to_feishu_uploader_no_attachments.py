#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GTTC报告数据到飞书多维表的上传脚本（不包含附件上传）
专注于数据同步，跳过文件处理以简化测试
"""

import json
import os
import time
from datetime import datetime

# 导入自定义模块
import feishu_config as config
import feishu_uploader_utils as feishu_utils
from gttc_scraper import GTTCReportScraper

def process_single_report_no_attachments(report_data):
    """处理单条报告数据，不包含附件处理"""
    feishu_record_fields = {}
    
    # 数字字段列表（基于飞书字段类型）
    number_fields = {'序号', '金额'}
    
    # 字段映射 (不包括附件链接)
    for scraper_key, feishu_key in config.FIELD_MAPPING.items():
        if feishu_key and scraper_key in report_data:
            # 跳过附件字段
            if scraper_key.endswith('下载链接') or scraper_key.endswith('_链接'):
                continue
                
            # 处理常规字段
            value = report_data[scraper_key]
            
            # 特殊处理数字字段
            if feishu_key in number_fields:
                try:
                    # 尝试转换为数字
                    if value is None or value == "":
                        feishu_record_fields[feishu_key] = 0  # 空值设为0
                    elif isinstance(value, (int, float)):
                        feishu_record_fields[feishu_key] = value
                    else:
                        # 字符串转数字，去除可能的非数字字符
                        str_value = str(value).strip()
                        # 去除逗号和其他可能的分隔符
                        str_value = str_value.replace(',', '').replace('，', '')
                        if str_value:
                            # 尝试转换为整数或浮点数
                            if '.' in str_value:
                                feishu_record_fields[feishu_key] = float(str_value)
                            else:
                                feishu_record_fields[feishu_key] = int(str_value)
                        else:
                            feishu_record_fields[feishu_key] = 0
                except (ValueError, TypeError) as e:
                    print(f"警告: 无法将 {feishu_key} 的值 '{value}' 转换为数字，使用默认值 0。错误: {e}")
                    feishu_record_fields[feishu_key] = 0
            else:
                # 普通字符串字段
                if isinstance(value, (int, float)):
                     feishu_record_fields[feishu_key] = value
                else:
                     feishu_record_fields[feishu_key] = str(value).strip() if value is not None else ""

    return {"fields": feishu_record_fields}

def main():
    print("开始执行GTTC报告数据到飞书多维表的上传流程（无附件版本）...")

    # 1. 初始化GTTC抓取器并抓取数据
    print("\n步骤 1: 从GTTC抓取报告数据...")
    scraper = GTTCReportScraper()
    
    # 使用较小的日期范围进行测试
    custom_params = {
        'AccceptTimeFrom': '2025-05-29', # 最近几天的数据
        'AccceptTimeTo': datetime.today().strftime('%Y-%m-%d'),
        'IsPassReport': '1' # 只获取合格报告
    }
    print(f"使用查询参数: {custom_params}")
    
    response = scraper.make_request(params=custom_params)
    if not response:
        print("从GTTC获取数据失败，程序终止。")
        return
    
    gttc_data = scraper.parse_html_table(response.text)
    if not gttc_data:
        print("未从GTTC解析到任何数据，程序终止。")
        return
    
    print(f"成功从GTTC抓取并解析到 {len(gttc_data)} 条报告数据。")
    
    # 只处理前10条记录进行测试
    test_data = gttc_data[:10]
    print(f"处理前 {len(test_data)} 条记录进行测试...")

    # 2. 清空飞书多维表中的所有旧数据
    print(f"\n步骤 2: 清空飞书多维表 (App: {config.APP_TOKEN}, Table: {config.TABLE_ID}) 中的所有记录...")
    if not feishu_utils.clear_all_records(config.APP_TOKEN, config.TABLE_ID):
        print("清空飞书表格失败，程序终止以防止数据不一致。")
        return
    else:
        print("飞书多维表已成功清空。")

    # 3. 处理每条记录
    print("\n步骤 3: 处理抓取到的数据...")
    feishu_records_to_upload = []
    
    for i, report_item in enumerate(test_data):
        print(f"处理第 {i+1}/{len(test_data)} 条报告 (报告编号: {report_item.get('报告编号', 'N/A')})...")
        
        processed_record = process_single_report_no_attachments(report_item)
        if processed_record and processed_record["fields"]:
            feishu_records_to_upload.append(processed_record)
        else:
            print(f"警告: 报告 (编号: {report_item.get('报告编号', 'N/A')}) 处理后没有有效字段，将跳过此条记录。")

    if not feishu_records_to_upload:
        print("没有成功处理任何记录以供上传到飞书。程序终止。")
        return

    print(f"\n共准备了 {len(feishu_records_to_upload)} 条记录待上传到飞书。")
    if feishu_records_to_upload:
        print("预览第一条待上传到飞书的记录 (处理后): ")
        print(json.dumps(feishu_records_to_upload[0], ensure_ascii=False, indent=2))

    # 4. 批量上传数据到飞书
    print(f"\n步骤 4: 批量添加处理后的记录到飞书多维表...")
    success_upload, added_count, failed_upload_info = feishu_utils.batch_add_records(
        config.APP_TOKEN, 
        config.TABLE_ID, 
        feishu_records_to_upload
    )

    if success_upload:
        print(f"\n成功上传 {added_count} 条记录到飞书多维表！")
    else:
        print(f"\n上传记录到飞书多维表过程中发生错误。")
        print(f"成功添加 {added_count} 条记录。")
        if failed_upload_info:
            print(f"{len(failed_upload_info)} 条记录可能上传失败。")

    print("\nGTTC报告数据到飞书多维表的上传流程结束（无附件版本）。")

if __name__ == "__main__":
    main() 