# GTTC质检报告转飞书多维表工具

## 项目概述

这是一个自动化工具，用于从GTTC（广东省纺织产品质量监督检验中心）网站抓取质检报告数据，并将数据批量上传到飞书多维表中。

## 功能特性

- ✅ **数据抓取**：从GTTC网站自动抓取质检报告数据
- ✅ **数据清理**：自动处理和格式化数据
- ✅ **飞书集成**：将数据批量同步到飞书多维表
- ⚠️ **附件上传**：下载和上传报告附件（需要额外配置）
- ✅ **类型转换**：自动处理数字、文本等不同数据类型
- ✅ **错误处理**：完善的错误处理和重试机制

## 文件结构

```
广检质检报告转多维表/
├── feishu_config.py                          # 飞书应用配置
├── feishu_uploader_utils.py                  # 飞书API工具函数
├── gttc_scraper.py                          # GTTC数据抓取器
├── gttc_to_feishu_uploader.py               # 完整版本（包含附件）
├── gttc_to_feishu_uploader_no_attachments.py # 简化版本（仅数据）
├── test_large_range.py                      # 小批量测试脚本
├── usage_example.py                         # 飞书字段结构检查
├── requirements.txt                         # Python依赖
└── README.md                               # 本文件
```

## 安装和配置

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置飞书应用

在 `feishu_config.py` 中配置您的飞书应用信息：

```python
# 多维表的Base ID (appToken)
APP_TOKEN = "您的appToken"

# Table(数据表) ID
TABLE_ID = "您的tableId"

# 开放平台应用 App ID
APP_ID = "您的appId"

# 开放平台应用 App Secret
APP_SECRET = "您的appSecret"
```

## 使用方法

### 基础数据同步（推荐开始）

使用简化版本，只同步数据不处理附件：

```bash
python gttc_to_feishu_uploader_no_attachments.py
```

这个版本：
- ✅ 稳定可靠
- ✅ 同步所有文本和数字字段
- ✅ 自动处理数据类型转换
- ✅ 快速执行

### 完整版本（包含附件）

```bash
python gttc_to_feishu_uploader.py
```

**注意**：附件上传功能需要额外的飞书API配置，可能需要进一步调试。

### 小批量测试

```bash
python test_large_range.py
```

测试脚本会：
- 只处理前5条记录
- 询问确认后再上传
- 提供详细的调试信息

### 检查飞书表格结构

```bash
python usage_example.py
```

这会显示您的飞书多维表的所有字段名和类型，用于调试字段映射问题。

## 配置说明

### 字段映射

在 `feishu_config.py` 的 `FIELD_MAPPING` 中配置GTTC字段到飞书字段的映射：

```python
FIELD_MAPPING = {
    'GTTC字段名': '飞书字段名',
    '序号': '序号',              # 数字类型
    '报告编号': '报告编号',        # 文本类型
    '金额': '金额',              # 数字类型
    # ... 更多字段映射
}
```

### 数据类型处理

工具会自动处理以下数据类型：
- **数字字段**：`序号`, `金额` - 自动转换为数字类型
- **文本字段**：其他所有字段 - 转换为字符串类型
- **附件字段**：以`下载链接`或`_链接`结尾的字段

## 故障排除

### 常见问题

1. **`FieldNameNotFound`错误**
   - 检查飞书表格中的字段名是否与配置文件中的映射一致
   - 运行 `python usage_example.py` 查看实际字段名

2. **`NumberFieldConvFail`错误**
   - 数字字段（如序号、金额）的值无法转换为数字
   - 工具会自动处理，将无效值设为0

3. **网络连接错误**
   - 检查网络连接
   - 确保可以访问GTTC网站和飞书API

4. **附件上传失败**
   - 目前附件上传需要进一步的飞书API配置
   - 建议先使用无附件版本进行数据同步

### 调试步骤

1. **测试飞书连接**：
   ```bash
   python usage_example.py
   ```

2. **小批量测试**：
   ```bash
   python test_large_range.py
   ```

3. **查看详细日志**：
   脚本会输出详细的执行日志，包括API请求状态和错误信息

## 成功案例

**最近的测试结果**：
- ✅ 从GTTC成功抓取29条报告数据
- ✅ 成功清空飞书多维表
- ✅ 成功处理数据类型转换
- ✅ 成功上传10条测试记录到飞书

## 自定义参数

可以在脚本中修改查询参数：

```python
custom_params = {
    'AccceptTimeFrom': '2025-05-01',  # 开始日期
    'AccceptTimeTo': '2025-05-30',    # 结束日期
    'IsPassReport': '1',              # 1=合格报告, 0=不合格, ''=全部
    'DelegateOrgName': '',            # 委托单位名称（可选）
    'SampleName': '',                 # 样品名称（可选）
}
```

## 注意事项

1. **数据覆盖**：脚本会清空飞书表格中的所有现有数据，请确保备份重要数据
2. **API限制**：注意飞书API的调用频率限制
3. **数据量**：对于大量数据，建议分批处理
4. **权限**：确保您的飞书应用有足够的权限访问多维表

## 支持的GTTC字段

工具支持以下GTTC字段的同步：
- 序号、报告编号、受理时间、委托单位
- 样品名称、款号、颜色、商标、面料编号
- 生产单位、缴费单位、检验项目、检测进度
- 报告结果、不合格项目、预计发证时间、出证时间
- 金额、费用交付状态、报告寄出信息、备注

## 更新日志

### v1.0 (2025-05-30)
- ✅ 完成基础数据同步功能
- ✅ 支持数字类型自动转换
- ✅ 完善错误处理机制
- ⚠️ 附件上传功能需要进一步优化

## 联系支持

如果遇到问题，请检查：
1. 飞书应用配置是否正确
2. 网络连接是否正常
3. 字段映射是否匹配
4. 查看脚本输出的错误日志 