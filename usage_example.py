#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查飞书多维表的字段结构
"""

import json
import feishu_config as config
import feishu_uploader_utils as feishu_utils

def check_feishu_table_structure():
    """检查飞书多维表的字段结构"""
    print("正在检查飞书多维表的字段结构...")
    
    try:
        # 获取表格的字段信息
        url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{config.APP_TOKEN}/tables/{config.TABLE_ID}/fields"
        response = feishu_utils._make_feishu_request("GET", url)
        
        if response.status_code == 200:
            data = response.json().get("data", {})
            fields = data.get("items", [])
            
            print(f"表格共有 {len(fields)} 个字段：")
            print("-" * 50)
            
            for i, field in enumerate(fields, 1):
                field_name = field.get("field_name", "未知")
                field_type = field.get("type", "未知")
                field_id = field.get("field_id", "未知")
                
                print(f"{i:2d}. 字段名: {field_name}")
                print(f"    类型: {field_type}")
                print(f"    ID: {field_id}")
                print()
            
            # 保存字段信息到文件
            with open("feishu_table_fields.json", "w", encoding="utf-8") as f:
                json.dump(fields, f, ensure_ascii=False, indent=2)
            print("字段信息已保存到 feishu_table_fields.json")
            
            # 检查我们配置的字段是否存在
            print("\n检查配置的字段映射：")
            print("-" * 50)
            
            existing_field_names = [field.get("field_name") for field in fields]
            
            for gttc_field, feishu_field in config.FIELD_MAPPING.items():
                if feishu_field:
                    if feishu_field in existing_field_names:
                        print(f"✓ {gttc_field} -> {feishu_field} (存在)")
                    else:
                        print(f"✗ {gttc_field} -> {feishu_field} (不存在)")
                        # 寻找相似的字段名
                        similar = [name for name in existing_field_names if feishu_field.replace("附件", "") in name or feishu_field.replace("状态", "") in name]
                        if similar:
                            print(f"  可能的字段: {similar}")
            
        else:
            print(f"获取字段信息失败: {response.status_code}")
            print(f"响应: {response.text}")
            
    except Exception as e:
        print(f"检查表格结构时发生错误: {e}")

if __name__ == "__main__":
    check_feishu_table_structure() 