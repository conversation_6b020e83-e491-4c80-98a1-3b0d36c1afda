#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本：使用较小的数据范围来测试GTTC到飞书的上传功能
"""

import json
import os
import time
from datetime import datetime, timedelta

# 导入自定义模块
import feishu_config as config
import feishu_uploader_utils as feishu_utils
from gttc_scraper import GTTCReportScraper
from gttc_to_feishu_uploader import process_single_report, TEMP_DOWNLOAD_DIR

def test_small_batch():
    """测试少量数据的上传"""
    print("开始测试GTTC报告数据到飞书多维表的上传流程（小批量测试）...")
    
    # 1. 测试飞书连接
    print("\n步骤 1: 测试飞书API连接...")
    try:
        token = feishu_utils.get_tenant_access_token()
        if token:
            print(f"✓ 成功获取飞书访问令牌 (前10位): {token[:10]}")
        else:
            print("✗ 获取飞书访问令牌失败")
            return
    except Exception as e:
        print(f"✗ 飞书连接测试失败: {e}")
        return

    # 2. 获取最近几天的少量数据
    print("\n步骤 2: 从GTTC抓取少量报告数据...")
    scraper = GTTCReportScraper()
    
    # 只获取最近3天的数据来测试
    end_date = datetime.today()
    start_date = end_date - timedelta(days=3)
    
    custom_params = {
        'AccceptTimeFrom': start_date.strftime('%Y-%m-%d'),
        'AccceptTimeTo': end_date.strftime('%Y-%m-%d'),
        'IsPassReport': '1'
    }
    print(f"使用查询参数: {custom_params}")
    
    response = scraper.make_request(params=custom_params)
    if not response:
        print("✗ 从GTTC获取数据失败")
        return
    
    gttc_data = scraper.parse_html_table(response.text)
    if not gttc_data:
        print("✗ 未从GTTC解析到任何数据")
        return
    
    print(f"✓ 成功获取 {len(gttc_data)} 条报告数据")
    
    # 3. 只处理前5条记录进行测试
    test_records = gttc_data[:5]
    print(f"\n步骤 3: 处理前 {len(test_records)} 条记录进行测试...")
    
    feishu_records_to_upload = []
    
    for i, report_item in enumerate(test_records):
        print(f"\n处理第 {i+1}/{len(test_records)} 条报告 (报告编号: {report_item.get('报告编号', 'N/A')})...")
        
        try:
            processed_record = process_single_report(report_item, scraper.session)
            if processed_record and processed_record["fields"]:
                feishu_records_to_upload.append(processed_record)
                print(f"✓ 成功处理报告 {report_item.get('报告编号', 'N/A')}")
            else:
                print(f"⚠ 报告 {report_item.get('报告编号', 'N/A')} 处理后没有有效字段")
        except Exception as e:
            print(f"✗ 处理报告 {report_item.get('报告编号', 'N/A')} 时发生错误: {e}")
        
        time.sleep(1)  # 增加延迟避免过于频繁的请求

    if not feishu_records_to_upload:
        print("\n✗ 没有成功处理任何记录")
        return

    print(f"\n✓ 共准备了 {len(feishu_records_to_upload)} 条记录待上传到飞书")
    
    # 4. 显示第一条记录的预览
    if feishu_records_to_upload:
        print("\n第一条待上传记录的预览:")
        print(json.dumps(feishu_records_to_upload[0], ensure_ascii=False, indent=2))

    # 5. 询问是否继续上传到飞书
    user_input = input(f"\n是否将这 {len(feishu_records_to_upload)} 条记录上传到飞书多维表？(y/N): ")
    if user_input.lower() != 'y':
        print("用户取消上传，测试结束。")
        return

    # 6. 上传到飞书
    print(f"\n步骤 4: 上传 {len(feishu_records_to_upload)} 条记录到飞书多维表...")
    try:
        success_upload, added_count, failed_upload_info = feishu_utils.batch_add_records(
            config.APP_TOKEN, 
            config.TABLE_ID, 
            feishu_records_to_upload
        )

        if success_upload:
            print(f"✓ 成功上传 {added_count} 条记录到飞书多维表！")
        else:
            print(f"⚠ 上传过程中遇到问题，成功添加 {added_count} 条记录")
            if failed_upload_info:
                print(f"可能失败的记录数: {len(failed_upload_info)}")
    except Exception as e:
        print(f"✗ 上传到飞书时发生错误: {e}")

    # 7. 清理临时文件
    try:
        if os.path.exists(TEMP_DOWNLOAD_DIR):
            for item in os.listdir(TEMP_DOWNLOAD_DIR):
                item_path = os.path.join(TEMP_DOWNLOAD_DIR, item)
                if os.path.isfile(item_path):
                    os.remove(item_path)
            if not os.listdir(TEMP_DOWNLOAD_DIR):
                os.rmdir(TEMP_DOWNLOAD_DIR)
            print("✓ 已清理临时文件")
    except Exception as e:
        print(f"⚠ 清理临时文件时发生错误: {e}")

    print("\n测试完成！")

if __name__ == "__main__":
    test_small_batch() 