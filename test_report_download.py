#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报告书下载问题的单元测试
用于定向分析和解决报告书无法下载的问题
"""

import json
import os
from gttc_scraper import GTTCReportScraper
from datetime import datetime, timedelta

def test_gttc_data_structure():
    """测试GTTC抓取的数据结构，特别是报告书下载链接字段"""
    print("=" * 60)
    print("测试1: 分析GTTC抓取的数据结构")
    print("=" * 60)
    
    # 1. 初始化抓取器
    scraper = GTTCReportScraper()
    
    # 2. 使用最近3天的数据进行测试
    today = datetime.now()
    start_date = today - timedelta(days=3)
    end_date = today
    
    date_params = {
        "Report_Date1": start_date.strftime("%Y-%m-%d"),
        "Report_Date2": end_date.strftime("%Y-%m-%d"),
    }
    
    print(f"查询参数: {date_params}")
    
    # 3. 发送请求
    response = scraper.make_request(params=date_params)
    if not response:
        print("❌ 请求失败")
        return False
    
    print(f"✅ 请求成功，状态码: {response.status_code}")
    
    # 4. 解析数据
    gttc_data = scraper.parse_html_table(response.text)
    if not gttc_data:
        print("❌ 解析失败")
        return False
    
    print(f"✅ 解析成功，共 {len(gttc_data)} 条记录")
    
    # 5. 分析报告书下载链接字段
    print("\n分析报告书下载链接字段:")
    print("-" * 40)
    
    report_link_analysis = {
        'total': len(gttc_data),
        'has_http_url': 0,
        'only_number': 0,
        'empty_or_none': 0,
        'status_可下载': 0,
        'status_不可用': 0,
        'examples': {
            'http_url': [],
            'only_number': [],
            'empty': []
        }
    }
    
    for i, item in enumerate(gttc_data[:20]):  # 只分析前20条
        report_id = item.get('报告编号', 'N/A')
        report_link = item.get('报告书下载链接', '')
        report_status = item.get('报告书', '')
        detection_status = item.get('检测进度', '')
        
        print(f"\n记录 {i+1}:")
        print(f"  报告编号: {report_id}")
        print(f"  报告书状态: '{report_status}'")
        print(f"  检测进度: '{detection_status}'")
        print(f"  报告书下载链接: '{report_link}'")
        print(f"  链接类型: {type(report_link)}")
        print(f"  链接长度: {len(str(report_link))}")
        
        # 统计分析
        if report_status == '可下载':
            report_link_analysis['status_可下载'] += 1
        elif report_status == '不可用':
            report_link_analysis['status_不可用'] += 1
            
        if report_link and str(report_link).strip():
            link_str = str(report_link).strip()
            if link_str.startswith(('http:', 'https:')):
                report_link_analysis['has_http_url'] += 1
                if len(report_link_analysis['examples']['http_url']) < 3:
                    report_link_analysis['examples']['http_url'].append({
                        'report_id': report_id,
                        'link': link_str[:100] + '...' if len(link_str) > 100 else link_str,
                        'status': report_status
                    })
            else:
                report_link_analysis['only_number'] += 1
                if len(report_link_analysis['examples']['only_number']) < 3:
                    report_link_analysis['examples']['only_number'].append({
                        'report_id': report_id,
                        'link': link_str,
                        'status': report_status
                    })
        else:
            report_link_analysis['empty_or_none'] += 1
            if len(report_link_analysis['examples']['empty']) < 3:
                report_link_analysis['examples']['empty'].append({
                    'report_id': report_id,
                    'link': repr(report_link),
                    'status': report_status
                })
    
    # 6. 输出分析结果
    print("\n" + "=" * 60)
    print("数据结构分析结果:")
    print("=" * 60)
    print(f"总记录数: {report_link_analysis['total']}")
    print(f"报告书状态为'可下载': {report_link_analysis['status_可下载']}")
    print(f"报告书状态为'不可用': {report_link_analysis['status_不可用']}")
    print(f"包含HTTP URL的链接: {report_link_analysis['has_http_url']}")
    print(f"只有编号的链接: {report_link_analysis['only_number']}")
    print(f"空或None的链接: {report_link_analysis['empty_or_none']}")
    
    print("\n示例数据:")
    print("-" * 30)
    
    if report_link_analysis['examples']['http_url']:
        print("包含HTTP URL的示例:")
        for example in report_link_analysis['examples']['http_url']:
            print(f"  {example['report_id']} ({example['status']}): {example['link']}")
    
    if report_link_analysis['examples']['only_number']:
        print("\n只有编号的示例:")
        for example in report_link_analysis['examples']['only_number']:
            print(f"  {example['report_id']} ({example['status']}): {example['link']}")
    
    if report_link_analysis['examples']['empty']:
        print("\n空链接的示例:")
        for example in report_link_analysis['examples']['empty']:
            print(f"  {example['report_id']} ({example['status']}): {example['link']}")
    
    return True

def test_specific_report_processing():
    """测试特定报告的处理逻辑"""
    print("\n" + "=" * 60)
    print("测试2: 测试特定报告的处理逻辑")
    print("=" * 60)
    
    # 模拟不同类型的报告数据
    test_cases = [
        {
            'name': '报告书可下载且有完整URL',
            'data': {
                '报告编号': 'TEST001',
                '报告书': '可下载',
                '检测进度': '已出证',
                '报告书下载链接': 'https://www.gttc.net.cn/down/NewSystem//202505/20250527/14/abdef1be/测试报告_TEST001.pdf'
            }
        },
        {
            'name': '报告书可下载但只有编号',
            'data': {
                '报告编号': 'TEST002',
                '报告书': '可下载',
                '检测进度': '已出证',
                '报告书下载链接': 'TEST002'
            }
        },
        {
            'name': '报告书不可用且只有编号',
            'data': {
                '报告编号': 'TEST003',
                '报告书': '不可用',
                '检测进度': '正在检测',
                '报告书下载链接': 'TEST003'
            }
        },
        {
            'name': '报告书可下载但链接为空',
            'data': {
                '报告编号': 'TEST004',
                '报告书': '可下载',
                '检测进度': '已出证',
                '报告书下载链接': ''
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {test_case['name']}")
        print("-" * 40)
        
        data = test_case['data']
        report_id = data['报告编号']
        download_url = data.get('报告书下载链接')
        report_status = data.get('报告书', '').strip()
        detection_status = data.get('检测进度', '').strip()
        
        print(f"输入数据:")
        print(f"  报告编号: {report_id}")
        print(f"  报告书状态: '{report_status}'")
        print(f"  检测进度: '{detection_status}'")
        print(f"  下载链接: '{download_url}'")
        
        # 模拟处理逻辑
        should_download = False
        reason = ""
        
        if download_url and str(download_url).strip() and str(download_url).lower() != "不可用":
            download_url_str = str(download_url).strip()
            
            if download_url_str.startswith(('http:', 'https:')):
                # 处理多行URL的情况
                urls = [url.strip() for url in download_url_str.split('\n') if url.strip() and url.strip().startswith(('http:', 'https:'))]
                
                if urls:
                    should_download = True
                    reason = f"找到有效URL: {urls[0]}"
                else:
                    reason = "URL格式无效"
            else:
                reason = f"只有编号 '{download_url_str}'，报告书可能还未生成"
        else:
            reason = f"链接无效或不可用: '{download_url}'"
        
        print(f"处理结果:")
        print(f"  是否应该下载: {should_download}")
        print(f"  原因: {reason}")
        
        # 分析问题
        if report_status == '可下载' and not should_download:
            print(f"  ⚠️  问题: 报告书状态为'可下载'但无法下载!")
            print(f"      这可能是数据不一致的问题")

def main():
    """主测试函数"""
    print("开始报告书下载问题的单元测试...")
    
    # 测试1: 分析数据结构
    if not test_gttc_data_structure():
        print("❌ 数据结构测试失败")
        return
    
    # 测试2: 测试处理逻辑
    test_specific_report_processing()
    
    print("\n" + "=" * 60)
    print("测试完成!")
    print("=" * 60)

if __name__ == "__main__":
    main()
